#include "bka30.h"
// 加减速控制参数
#define ACCEL_STAGES 32      // 加速阶段数
u8 ACCEL_STEPS = 8;             // 每个阶段的步数
u16 MIN_SPEED = 500;            // 最低速度
u8 MAX_SPEED = 20;              // 最高速度
u8 fil = 15;                    // 滤波步数


#define MAX_BACK_STEPS 3240   // 归零时最大反转步数

// 普通计数相关定义
static volatile u32 step_counter = 0;   // 步进计数器

/*------------------------------------------------------------------------------
线圈1的CCR值数据表（余弦）
-------------------------------------------------------------------------------*/
static const uint8_t CosTable[] =
{
    255, 246, 221, 180, 127, 65, 0, 65, 127, 180, 221, 246,
    255, 246, 221, 180, 127, 65, 0, 65, 127, 180, 221, 246
};

/*------------------------------------------------------------------------------
线圈2的CCR值数据表（正弦）
-------------------------------------------------------------------------------*/
static const uint8_t SinTable[] =
{
    127, 180, 221, 246, 255, 246, 221, 180, 127, 65, 0, 65,
    127, 180, 221, 246, 255, 246, 221, 180, 127, 65, 0, 65
};

// 将每个步骤分为4个微步，每个周期共24步
// 这个数组存储了24个微步状态下的PWM值（0-255）
/* static  const uint8_t microStepState[] = {251, 238, 218, 191,
                                160, 128, 95, 64,
                                37, 17, 4, 0,
                                4, 17, 37, 64,
                                95, 128, 160, 191,
                                218, 238, 251, 255};*/

static u8 now_sheep = 0;

// 微步模式下各引脚的起始索引偏移量
//#define STARTINDEX_PIN1 10  // 0 // 23-5
//#define STARTINDEX_PIN2 18 // 23-13
//#define STARTINDEX_PIN3 2   // 23-21
//#define STARTINDEX_PIN4 2   // 23-21

// 全局变量
static u8 M_Phase = 0;        // 当前相位
static u8 motor_dir = 0;      // 电机方向(0反转，1正转)
static u16 target_step = 0;   // 目标步数
static u16 current_step = 0;  // 当前步数
static u16 steps_since_start = 0; // 自启动以来的步数
static u32 steps_delay = 0;   // 当前延迟步数
static u32 last_step = 0;     // 上次更新的步数
static _Bool is_running = 0;  // 电机是否在运行
static _Bool is_homing = 0;   // 电机是否正在归零
static _Bool is_max_speed_mode = 0;  // 电机是否在最大速度模式

// 加速表
static u16 accel_table[ACCEL_STAGES];

/*------------------------------------------------------------------------------
步进计数器增加函数(在主循环中调用)
----------------------------------------------------------------------*/
void MOTOR_StepCounter_Increment(void)
{
    step_counter++;
}

/*------------------------------------------------------------------------------
获取当前步进计数值
----------------------------------------------------------------------*/
static u32 MOTOR_GetStepCount(void)
{
    return step_counter;
}

/*------------------------------------------------------------------------------
步进电机端口初始化
----------------------------------------------------------------------*/
void MOTOR_Init(void)
{
    GPIO_Init(GPIOC, GPIO_PIN_7, GPIO_MODE_OUT_PP_LOW_FAST);
    GPIO_Init(GPIOC, GPIO_PIN_6, GPIO_MODE_OUT_PP_LOW_FAST);

    TIM1_TimeBaseInit(0, TIM1_COUNTERMODE_UP, 255, 0x00);
    //                  模式              设置比较输出          设置比较互补输出    比较值      输出比较极性         输出互补比较极性        输出比较空闲状态    输出互补比较空闲状态
    // TIM1_OC1Init(TIM1_OCMODE_PWM1, TIM1_OUTPUTSTATE_ENABLE,TIM1_OUTPUTNSTATE_DISABLE,0, TIM1_OCPOLARITY_HIGH, TIM1_OCNPOLARITY_HIGH,TIM1_OCIDLESTATE_RESET, TIM1_OCNIDLESTATE_RESET);
    // TIM1_OC2Init(TIM1_OCMODE_PWM1, TIM1_OUTPUTSTATE_ENABLE,TIM1_OUTPUTNSTATE_DISABLE,0, TIM1_OCPOLARITY_HIGH, TIM1_OCNPOLARITY_HIGH,TIM1_OCIDLESTATE_RESET, TIM1_OCNIDLESTATE_RESET);
    TIM1_OC3Init(TIM1_OCMODE_PWM1, TIM1_OUTPUTSTATE_ENABLE,TIM1_OUTPUTNSTATE_DISABLE,0, TIM1_OCPOLARITY_HIGH, TIM1_OCNPOLARITY_HIGH,TIM1_OCIDLESTATE_RESET, TIM1_OCNIDLESTATE_RESET);
    TIM1_OC4Init(TIM1_OCMODE_PWM1, TIM1_OUTPUTSTATE_ENABLE, 0, TIM1_OCPOLARITY_HIGH, TIM1_OCIDLESTATE_RESET);
    TIM1_Cmd(ENABLE);
    TIM1_CtrlPWMOutputs(ENABLE);

    // 初始化加速表
    float factor = 1.0;
    for (u8 i = 0; i < ACCEL_STAGES; i++) {
        // 将时间延迟转换为计数延迟
        accel_table[i] = (u16)(MIN_SPEED * factor);
        // 确保最小值
        if (accel_table[i] < MAX_SPEED) accel_table[i] =MAX_SPEED;
        factor *= 0.80;  // 每个阶段延迟时间递减20%
    }

    // 初始化步进计数器
    step_counter = 0;
    is_homing = 0;
}

/*------------------------------------------------------------------------------
电机功率关闭函数
----------------------------------------------------------------------*/
void MOTOR_PowerOff(void)
{
    GPIO_WriteLow(GPIOC, GPIO_PIN_7);
    GPIO_WriteLow(GPIOC, GPIO_PIN_6);
    TIM1_SetCompare3(0);
    TIM1_SetCompare4(0);
}

/*------------------------------------------------------------------------------
更新电机输出状态
----------------------------------------------------------------------*/
// static void MOTOR_UpdateIO(void)
// {
// // 微步模式：使用PWM输出不同的占空比
//     TIM1_SetCompare1(microStepState[(M_Phase + STARTINDEX_PIN1) % 24]);
//     TIM1_SetCompare2(microStepState[(M_Phase + STARTINDEX_PIN2) % 24]);
//     TIM1_SetCompare3(microStepState[(M_Phase + STARTINDEX_PIN3) % 24]);
//     TIM1_SetCompare4(microStepState[(M_Phase + STARTINDEX_PIN4) % 24]);
// }

static void MOTOR_UpdateIO(void)
{
    // 根据当前相位设置PWM值
    TIM1_SetCompare3(CosTable[M_Phase]);
    TIM1_SetCompare4(SinTable[M_Phase]);

    // 根据相位控制IO引脚
    switch (M_Phase) {
        case 6:
            GPIO_WriteHigh(GPIOC, GPIO_PIN_7);
            TIM1_OC3PolarityConfig(TIM1_OCPOLARITY_LOW);
            break;
        case 10:
            GPIO_WriteHigh(GPIOC, GPIO_PIN_6);
            TIM1_OC4PolarityConfig(TIM1_OCPOLARITY_LOW);
            break;
        case 18:
            GPIO_WriteLow(GPIOC, GPIO_PIN_7);
            TIM1_OC3PolarityConfig(TIM1_OCPOLARITY_HIGH);
            break;
        case 22:
            GPIO_WriteLow(GPIOC, GPIO_PIN_6);
            TIM1_OC4PolarityConfig(TIM1_OCPOLARITY_HIGH);
            break;
        default:
            break;
    }
}

static void MOTOR_UpdateIO_down(void)
{
    // 根据当前相位设置PWM值
    TIM1_SetCompare3(CosTable[M_Phase]);
    TIM1_SetCompare4(SinTable[M_Phase]);

    // 根据相位控制IO引脚
    switch (M_Phase) {
       case 17:
            GPIO_WriteHigh(GPIOC, GPIO_PIN_7);  // PC7置高
            TIM1_OC3PolarityConfig(TIM1_OCPOLARITY_LOW);  // 通道3极性设为低
            break;
        case 21:
            GPIO_WriteHigh(GPIOC, GPIO_PIN_6);  // PC6置高
            TIM1_OC4PolarityConfig(TIM1_OCPOLARITY_LOW);  // 通道4极性设为低
            break;
        case 5:
            GPIO_WriteLow(GPIOC, GPIO_PIN_7);  // PC7置低
            TIM1_OC3PolarityConfig(TIM1_OCPOLARITY_HIGH);  // 通道3极性设为高
            break;
        case 9:
            GPIO_WriteLow(GPIOC, GPIO_PIN_6);  // PC6置低
            TIM1_OC4PolarityConfig(TIM1_OCPOLARITY_HIGH);  // 通道4极性设为高
            break;
        default:
            break;
    }
}

/*------------------------------------------------------------------------------
电机向前一步
----------------------------------------------------------------------*/
static void MOTOR_StepUp(void)
{
    if (M_Phase == 23)
        M_Phase = 0;
    else
        M_Phase++;

    MOTOR_UpdateIO();
    current_step++;
}

/*------------------------------------------------------------------------------
电机向后一步
----------------------------------------------------------------------*/
static void MOTOR_StepDown(void)
{
    if (M_Phase == 0)
        M_Phase = 23;
    else
        M_Phase--;

    MOTOR_UpdateIO_down();
    current_step--;
}

/*------------------------------------------------------------------------------
最大速度模式前进到下一状态（非阻塞式）
----------------------------------------------------------------------*/
static void MOTOR_Advance_MaxSpeed(void)
{
    // 检查是否到达目标
    if (current_step == target_step) {
        is_running = 0;  // 停止运行
        is_max_speed_mode = 0;  // 退出最大速度模式
        motor_dir = 0;   // 方向设为0

        // 如果正在归零过程中完成了目标步数，则将当前位置设置为0
        if (is_homing) {
            current_step = 0;  // 设置当前位置为0点
            is_homing = 0;     // 清除归零标志
        }

        return;
    }

    // 根据方向步进
    if (motor_dir == 1) {
        MOTOR_StepUp();
    } else {
        MOTOR_StepDown();
    }

    // 最大速度模式：固定使用最大速度
    steps_delay = MAX_SPEED;

    last_step = MOTOR_GetStepCount(); // 记录当前步数
}

/*------------------------------------------------------------------------------
前进到下一状态（非阻塞式）
----------------------------------------------------------------------*/
static void MOTOR_Advance(void)
{
    u16 steps_to_go;

    // 检查是否到达目标
    if (current_step == target_step) {
        is_running = 0;  // 停止运行
        motor_dir = 0;   // 方向设为0

        // 如果正在归零过程中完成了目标步数，则将当前位置设置为0
        if (is_homing) {
            current_step = 0;  // 设置当前位置为0点
            is_homing = 0;     // 清除归零标志
        }

        return;
    }

    // 根据方向步进
    if (motor_dir == 1) {
        MOTOR_StepUp();
    } else {
        MOTOR_StepDown();
    }

      // 减速阶段
    if (motor_dir == 1) {
        steps_to_go = target_step - current_step;
    } else {
        steps_to_go = current_step - target_step;
    }

    // 加速阶段
    if (steps_since_start < ACCEL_STAGES * ACCEL_STEPS && steps_to_go>steps_since_start) {
        now_sheep = steps_since_start / ACCEL_STEPS;
        steps_delay = accel_table[now_sheep];
       steps_since_start++;
    }
    else if(steps_to_go < steps_since_start && steps_to_go < ACCEL_STAGES * ACCEL_STEPS) {
        now_sheep = steps_to_go / ACCEL_STEPS;
        steps_delay = accel_table[now_sheep];
    }
    else if(now_sheep<ACCEL_STAGES-1)
    {
       steps_since_start = ACCEL_STEPS*now_sheep;
    }




    // 确保最小延迟
    if (steps_delay < MAX_SPEED)
        steps_delay = MAX_SPEED;

    last_step = MOTOR_GetStepCount(); // 记录当前步数
}

/*------------------------------------------------------------------------------
设置目标位置（非阻塞）
----------------------------------------------------------------------*/
void MOTOR_SetPosition(u16 pos, u8 dir)
{
    target_step = pos;
    motor_dir = dir;

    if (!is_running) {
        is_running = 1;
        steps_since_start = 0;
        steps_delay = accel_table[0]; // 从最慢的速度开始
        last_step = MOTOR_GetStepCount();
    }
}

/*------------------------------------------------------------------------------
设置目标位置
----------------------------------------------------------------------*/
void MOTOR_GoToPosition(u16 pos)
{
    // 如果电机已经在目标位置，不需要移动
    if (pos == current_step || pos == target_step) return;

    u16 temp = (pos > target_step) ? (pos - target_step) : (target_step - pos);
    // 计算与目标位置的差值
    int diff = pos - current_step;


    u8 dir;
    if (diff > 0) {
        // 正向差值
        dir = 1;  // 默认正转
    } else {
        // 负向差值
        dir = 0;  // 默认反转
    }


    if (motor_dir!=dir && temp<fil) return;


    if (!is_running || motor_dir!=dir) {
    // 重置计时器，避免可能的时间溢出导致错误的增量
    is_running = 1;  // 设置为运行状态
    last_step = MOTOR_GetStepCount();  // 记录当前时间
    steps_delay = 0;    // 初始延迟为0
    steps_since_start = 0;  // 重置自启动以来的步数
    }
    target_step = pos;
    motor_dir = dir;
}


/*------------------------------------------------------------------------------
紧急归零函数（断电时使用，以最快速度归零）
----------------------------------------------------------------------*/
void MOTOR_EmergencyHome(void)
{
    // 强制停止当前运行状态
    is_running = 0;
    is_homing = 0;
    is_max_speed_mode = 0;

    // 如果已经在零点，不需要移动
    if (current_step == 0) return;

    // 设置目标为0，方向为反转
    target_step = 0;
    motor_dir = 0;  // 反转到零点

    // 启动紧急最大速度模式
    is_max_speed_mode = 1;
    is_running = 1;

    // 使用最激进的速度设置（可以比正常MAX_SPEED更快）
    // 在断电情况下，我们需要尽快归零
    steps_delay = (MAX_SPEED > 15) ? 15 : MAX_SPEED;  // 使用更快的速度

    last_step = MOTOR_GetStepCount();
    steps_since_start = 0;
}



/*------------------------------------------------------------------------------
步进电机更新函数（非阻塞，需要在主循环中频繁调用）
----------------------------------------------------------------------*/
void MOTOR_Update(void)
{
    // 在每次主循环中增加步进计数器
    MOTOR_StepCounter_Increment();

    if (is_running) {
        u32 current_count = MOTOR_GetStepCount();
        u32 elapsed = current_count - last_step; // 步进计数差值

        if (elapsed >= steps_delay) {
            // 根据模式选择不同的前进函数
            if (is_max_speed_mode) {
                MOTOR_Advance_MaxSpeed();
            } else {
                MOTOR_Advance();
            }
        }
    }
}

/*------------------------------------------------------------------------------
获取电机运行状态
----------------------------------------------------------------------*/
_Bool MOTOR_IsRunning(void)
{
    return is_running;
}

/*------------------------------------------------------------------------------
检查电机是否正在归零
----------------------------------------------------------------------*/
_Bool MOTOR_IsHoming(void)
{
    return is_homing;
}

/*------------------------------------------------------------------------------
电机归零函数 - 反转最大步数以确保回到零点
----------------------------------------------------------------------*/
void MOTOR_Homing(void)
{
    // 如果电机已经在运行，不要启动归零
    if (is_running) {
        return;
    }

    // 设置归零标志
    is_homing = 1;

    // 将当前步数设为最大步数，然后反转到0
    current_step = MAX_BACK_STEPS+100;

    // 设置目标为0，反转到0点
    target_step = 0;
    motor_dir = 0; // 反转

    // 启动电机
    is_running = 1;
    steps_since_start = 0;
    steps_delay = accel_table[0]; // 从最慢的速度开始
    last_step = MOTOR_GetStepCount();
}
