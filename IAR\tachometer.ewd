<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>3</fileVersion>
    <configuration>
        <name>Debug</name>
        <toolchain>
            <name>STM8</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>C-SPY</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CSpyMandatory</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSpyInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSpyRunToEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSpyRunToName</name>
                    <state>main</state>
                </option>
                <option>
                    <name>CSpyMacOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyMacFile</name>
                    <state></state>
                </option>
                <option>
                    <name>DynDriver</name>
                    <state>STLINK_STM8</state>
                </option>
                <option>
                    <name>CSpyDDFOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyDDFFile</name>
                    <state>$TOOLKIT_DIR$\config\ddf\iostm8s003f3.ddf</state>
                </option>
                <option>
                    <name>CSpyEnableExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyImagesSuppressCheck1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyImagesPath1</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyImagesSuppressCheck2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyImagesPath2</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyImagesSuppressCheck3</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyImagesPath3</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyImagesOffset1</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyImagesOffset2</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyImagesOffset3</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyImagesUse1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyImagesUse2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyImagesUse3</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>SIMULATOR_STM8</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>SimMandatory</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>STICE_STM8</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>2</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>STiceMandatory</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STiceSuppressLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STiceVerifyLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STiceLogFileOver</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STiceLogFile</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>STiceUseSwim</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STiceOptionBytesSetupFileOver</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STiceOptionBytesSetupFile</name>
                    <state></state>
                </option>
                <option>
                    <name>STiceEraseMemory</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>STLINK_STM8</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>2</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>STlinkMandatory</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STlinkSuppressLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STlinkVerifyLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STlinkLogFileOver</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STlinkLogFile</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>STlinkOptionBytesSetupFileOver</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STlinkOptionBytesSetupFile</name>
                    <state></state>
                </option>
                <option>
                    <name>STlinkEraseMemory</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <debuggerPlugins>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\embOS\embOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\CodeCoverage\CodeCoverage.ENU.ewplugin</file>
                <loadFlag>1</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
        </debuggerPlugins>
    </configuration>
    <configuration>
        <name>Release</name>
        <toolchain>
            <name>STM8</name>
        </toolchain>
        <debug>0</debug>
        <settings>
            <name>C-SPY</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>CSpyMandatory</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSpyInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSpyRunToEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSpyRunToName</name>
                    <state>main</state>
                </option>
                <option>
                    <name>CSpyMacOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyMacFile</name>
                    <state></state>
                </option>
                <option>
                    <name>DynDriver</name>
                    <state>SIMULATOR_STM8</state>
                </option>
                <option>
                    <name>CSpyDDFOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyDDFFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyEnableExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyImagesSuppressCheck1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyImagesPath1</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyImagesSuppressCheck2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyImagesPath2</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyImagesSuppressCheck3</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyImagesPath3</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyImagesOffset1</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyImagesOffset2</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyImagesOffset3</name>
                    <state></state>
                </option>
                <option>
                    <name>CSpyImagesUse1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyImagesUse2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSpyImagesUse3</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>SIMULATOR_STM8</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>SimMandatory</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>STICE_STM8</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>2</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>STiceMandatory</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STiceSuppressLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STiceVerifyLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STiceLogFileOver</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STiceLogFile</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>STiceUseSwim</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STiceOptionBytesSetupFileOver</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STiceOptionBytesSetupFile</name>
                    <state></state>
                </option>
                <option>
                    <name>STiceEraseMemory</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>STLINK_STM8</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>2</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>STlinkMandatory</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STlinkSuppressLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STlinkVerifyLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STlinkLogFileOver</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STlinkLogFile</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>STlinkOptionBytesSetupFileOver</name>
                    <state>0</state>
                </option>
                <option>
                    <name>STlinkOptionBytesSetupFile</name>
                    <state></state>
                </option>
                <option>
                    <name>STlinkEraseMemory</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <debuggerPlugins>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\embOS\embOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\CodeCoverage\CodeCoverage.ENU.ewplugin</file>
                <loadFlag>1</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
        </debuggerPlugins>
    </configuration>
</project>
