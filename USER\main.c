#include "main.h"
#include <math.h>

#define ZHUAN_DU        0.03375             // 1转对应的度数
#define STEP_DU         12                  // 1度对应的步数
#define MAX_STEP        3240                // 最大步数

// 热敏电阻参数（需要根据实际使用的热敏电阻调整）
#define R0      50000   // 25°C时的电阻值，单位：欧姆
#define B_VALUE 3950    // B值，单位：开尔文
#define T0      298.15  // 参考温度，单位：开尔文（25°C）


volatile u16 NUMBER_MIN = 0;
volatile u16 NUMBER_MAX = 0;

volatile float step_unit = 0;
volatile float step_min = 0;

volatile u8 type_biao = 0;               // 0是温度表，1是压力表，2是电压表

volatile float R_init = 0.0f;          // 用于计算的电阻值


// 在文件开头添加全局变量定义（如果尚未定义）
u16 zzz = 0;

volatile u16 ICValue = 0;                               // 热敏电阻的电压
volatile u16 ICValue_temp=0;                            // 热敏电阻的电压的temp
volatile u16 Last_ICValue = 0;                          // 上一次热敏电阻的电压
volatile u16 step = 0;                                  // 当前步数设为0
volatile u16 vol = 0;                                   // 断电电压值
volatile u16 vol_temp = 0;                              // 断电电压值的temp
volatile u16 Last_vol = 0;


// 中值滤波相关变量
#define MEDIAN_FILTER_SIZE 3                // 中值滤波窗口大小
static u16 ICValue_Buffer[MEDIAN_FILTER_SIZE] = {0}; // 中值滤波缓冲区
static u8 ICValue_Index = 0;                // 当前缓冲区索引


float calculate_temp_b_param(float Rt) {
    // 转换为开尔文温度
    // float T0_K = 298.15f;
    // B参数方程计算
    float inv_T = (log(Rt / 50000) / 3950) + (1.0f / 298.15f);
    float tempC = (1.0f / inv_T) - 273.15f;
    return (tempC * 9.0f / 5.0f) + 32.0f;
}

void data_init(void)
{
  // 读取PA1、PA2、PA3引脚状态
  BitStatus  J4 = GPIO_ReadInputPin(GPIOA, GPIO_PIN_1);
  BitStatus  J5 = GPIO_ReadInputPin(GPIOA, GPIO_PIN_2);
  BitStatus  J6 = GPIO_ReadInputPin(GPIOA, GPIO_PIN_3);
  BitStatus  J10 = GPIO_ReadInputPin(GPIOB, GPIO_PIN_5);

  // 情况1: PA1高，PA2低，PA3低  水温、油温
  if (J4 && !J5 && !J6)
  {
    NUMBER_MIN = 100;
    NUMBER_MAX = 300;
    R_init = 7500.0f;
    type_biao = 0;
    MIN_SPEED = 500;
    MAX_SPEED = 15;
  }
  // 情况2: PA2高，PA1低，PA3低  变速箱油温
  else if (J5 && !J4 && !J6)
  {
    NUMBER_MIN = 80;
    NUMBER_MAX = 260;
    R_init = 7500.0f;
    type_biao = 0;
    ACCEL_STEPS = 8;
    MIN_SPEED = 500;
    MAX_SPEED = 15;
  }
  // 情况3: PA3高，PA1高，PA2低  油压
  else if (J6 && J4 && !J5)
  {
    NUMBER_MIN = 0;
    NUMBER_MAX = 140;
    R_init = 180.0f;
    type_biao = 1;
    ACCEL_STEPS = 8;
    MIN_SPEED = 500;
    MAX_SPEED = 30;
  }
  // 情况4: PA1高，PA2高，PA3低  电压
  else if (J4 && J5 && !J6)
  {
    NUMBER_MIN = 8;
    NUMBER_MAX = 18;
    type_biao = 2;
    ACCEL_STEPS = 8;
    MIN_SPEED = 1000;
    MAX_SPEED = 25;
    fil = 25;
  }


  step_unit =  3240.0f / (NUMBER_MAX - NUMBER_MIN);     //刻度单位
  step_min = NUMBER_MIN * step_unit;                    //刻度最小值
}


void ADC1_Config(void)
{
  ADC1_DeInit();
    // 初始化ADC1
    ADC1_Init(ADC1_CONVERSIONMODE_CONTINUOUS,
      ADC1_CHANNEL_4,
      ADC1_PRESSEL_FCPU_D2,
      ADC1_EXTTRIG_TIM,
      DISABLE,
      ADC1_ALIGN_RIGHT,
      ADC1_SCHMITTTRIG_ALL,
      DISABLE);

  // 启动ADC1
  ADC1_Cmd(ENABLE);
  ADC1_StartConversion();

}
void ADC_Init() {
    // 初始化ADC1，选择通道4
    ADC1_Init(ADC1_CONVERSIONMODE_SINGLE, ADC1_CHANNEL_4, ADC1_PRESSEL_FCPU_D2,
              ADC1_EXTTRIG_GPIO, DISABLE, ADC1_ALIGN_RIGHT, ADC1_SCHMITTTRIG_CHANNEL4, DISABLE);
    ADC1_Cmd(ENABLE);
}

uint16_t Read_ADC(ADC1_Channel_TypeDef channel) {
    ADC1_ConversionConfig(ADC1_CONVERSIONMODE_SINGLE, channel, ADC1_ALIGN_RIGHT);
    ADC1_StartConversion();
    while (!ADC1_GetFlagStatus(ADC1_FLAG_EOC));
    return ADC1_GetConversionValue();
}

// GPIO初始化
void gpio_init(void)
{
  // GPIO_Init(GPIOD, GPIO_PIN_4, GPIO_MODE_IN_FL_NO_IT);      // 档位1信号（配置为浮空输入无中断）
  GPIO_Init(GPIOD, GPIO_PIN_3, GPIO_MODE_IN_FL_NO_IT);          // ADC检测引脚
  GPIO_Init(GPIOD, GPIO_PIN_6, GPIO_MODE_IN_FL_NO_IT);          // 低电平断电引脚

  GPIO_Init(GPIOA, GPIO_PIN_1, GPIO_MODE_IN_PU_NO_IT);
  GPIO_Init(GPIOA, GPIO_PIN_2, GPIO_MODE_IN_PU_NO_IT);
  GPIO_Init(GPIOA, GPIO_PIN_3, GPIO_MODE_IN_PU_NO_IT);
  GPIO_Init(GPIOB, GPIO_PIN_5, GPIO_MODE_IN_PU_NO_IT);     //J10 默认高电平代表温度，低电平代表压力
}

// 电机自检
void step_init(void)
{
    // 使用归零功能，反转最大步数并设置为0点
    MOTOR_Homing();

    // 等待电机归零完成
    while(1) {
        MOTOR_Update();
        if(!MOTOR_IsRunning() && !MOTOR_IsHoming()) break;
    }


    // 正转到最大位置测试（使用最大速度）
    MOTOR_GoToPosition_speed(MAX_STEP);

    // 等待电机到达目标位置
    while(1) {
        MOTOR_Update();
        if(!MOTOR_IsRunning()) break;
    }


    // 反转回到0点（使用最大速度）
    MOTOR_GoToPosition_speed(0);

    // 等待电机到达目标位置
    while(1) {
        MOTOR_Update();
        if(!MOTOR_IsRunning()) break;
    }

}

// 中值滤波函数
u16 median_filter(u16 new_value)
{
    // 将新值添加到缓冲区
    ICValue_Buffer[ICValue_Index] = new_value;
    ICValue_Index = (ICValue_Index + 1) % MEDIAN_FILTER_SIZE;

    // 创建临时数组用于排序
    u16 temp_buffer[MEDIAN_FILTER_SIZE];
    for(u8 i = 0; i < MEDIAN_FILTER_SIZE; i++)
    {
        temp_buffer[i] = ICValue_Buffer[i];
    }

    // 冒泡排序
    for(u8 i = 0; i < MEDIAN_FILTER_SIZE - 1; i++)
    {
        for(u8 j = 0; j < MEDIAN_FILTER_SIZE - i - 1; j++)
        {
            if(temp_buffer[j] > temp_buffer[j + 1])
            {
                u16 temp = temp_buffer[j];
                temp_buffer[j] = temp_buffer[j + 1];
                temp_buffer[j + 1] = temp;
            }
        }
    }

    // 返回中值
    return temp_buffer[MEDIAN_FILTER_SIZE / 2];
}

int main(void)
{
    Sysclk_Init();                              // 初始化时钟
    gpio_init();                                // 初始化GPIO

    MOTOR_Init();
    // Init_UART1();                               // 初始化串口
    step_init();                                // 自检
    ADC1_Config();
    enableInterrupts();                         // 开启全局中断

    delay_ms(100);
    float temp = 0;
     data_init();

    while(1)
    {
      MOTOR_Update();

      zzz++;
      if(zzz>1000)
      {
          zzz = 0;

          // 掉电检测
          vol = Read_ADC(ADC1_CHANNEL_6);
          // vol = median_filter(vol_temp);

          if(type_biao != 2)
          {
              ICValue = Read_ADC(ADC1_CHANNEL_4);
              // ICValue = median_filter(ICValue_temp);
          }

      }

      if(type_biao == 2)
      {
        if(vol != Last_vol)
        {
            Last_vol = vol;
            temp = vol * 0.055;

            // 限制范围
            if(temp < NUMBER_MIN) temp = NUMBER_MIN;
            if(temp > NUMBER_MAX) temp = NUMBER_MAX;

            // 映射到步数
            step = (u16)(temp * step_unit - step_min);
            MOTOR_GoToPosition(step);
        }
      }
      else
      {
        if(vol<120)   // 掉电归0
        {
            MOTOR_GoToPosition(0);
        }
        else
        {
            if(ICValue != Last_ICValue)
            {
                Last_ICValue = ICValue;
                float R = (R_init * ICValue) / (1000.0f - ICValue);   // 根据ADC值计算电阻值

                if (R<=0)
                {
                    continue;
                }

                if(type_biao == 1)
                {
                    temp =  R / 1.2f;         // 计算压力
                }
                else if (type_biao == 0)
                {
                    temp = calculate_temp_b_param(R);         // 计算温度
                }


                // 限制温度范围
                if(temp < NUMBER_MIN) temp = NUMBER_MIN;
                if(temp > NUMBER_MAX) temp = NUMBER_MAX;

                // 将温度映射到步数
                step = (u16)(temp * step_unit - step_min);
                MOTOR_GoToPosition(step);
            }
        }
      }

    }
}







// 断言
#ifdef USE_FULL_ASSERT
void assert_failed(u8* file,u32 line)
{
    while(1)
    {

    }
}
#endif