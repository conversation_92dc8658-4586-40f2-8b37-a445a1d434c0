#ifndef _BKA30_H
#define _BKA30_H

#include "main.h"

extern u16 MIN_SPEED;
extern u8 ACCEL_STEPS;
extern u8 MAX_SPEED;
extern u8 fil;

// ���������ʼ��
void MOTOR_Init(void);

// ϵͳ�δ����������
void MOTOR_SysTick_Increment(void);  // ��1ms��ʱ�ж��е���

// ��������������
void MOTOR_StepCounter_Increment(void);  // ����ѭ���е���

// �������������API
void MOTOR_SetPosition(u16 pos, u8 dir);     // ����Ŀ��λ�ã�����������ָ������
void MOTOR_GoToPosition(u16 pos);            // ����Ŀ��λ�ã��Զ��������ŷ���
void MOTOR_Update(void);                     // ���״̬���º�������������
_Bool MOTOR_IsRunning(void);                 // ��ȡ�������״̬
void MOTOR_PowerOff(void);                   // ������ʹرպ���

// ������غ���
void MOTOR_Homing(void);                     // ������㺯��
_Bool MOTOR_IsHoming(void);                  // ������Ƿ����ڹ���

// ����ʽAPI
void MOTOR_RunMicroStep(uint8_t M_Dir, uint16_t M_Step, u32 times); // ����ָ������(����)

#endif