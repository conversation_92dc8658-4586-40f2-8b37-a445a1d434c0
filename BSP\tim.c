#include "tim.h"
/**
 * @brief  初始化TIM2输入通道1用于频率测量
 * @param  无
 * @retval 无
 * 
 * 功能说明:
 * - 通道1(TIM2_CH1)配置为输入捕获模式，用于测量外部信号频率
 * - 输入源为TI1FP1 (通道1输入)，检测上升沿
 * - 预分频为128，实际计时频率为系统时钟/128 = 125KHz (假设系统时钟为16MHz)
 * - 启用捕获中断，在中断中计算输入信号频率
 * - TIM2_CH1对应引脚：STM8S芯片上为PD4
 */
void TIM2_Capture1_Init(void)
{    
    // 1. 使能TIM2时钟
    CLK_PeripheralClockConfig(CLK_PERIPHERAL_TIMER2, ENABLE);
    /* 复位TIM2 */ 
    TIM2_DeInit();
    
    /* 配置TIM2基本参数 */
    /* 
     * 预分频比 = 31+1 = 32 (实际分频值)
     * 计数器时钟频率 = 系统时钟(16MHz) / 32 = 500KHz
     * 周期 = 65535 (最大值), 对应时间 = 524.28ms
     * 测量频率范围: 约 7.6Hz+
     * 增大预分频比可以更好地测量低频信号
     */
    TIM2_TimeBaseInit(TIM2_PRESCALER_32, 65535);
    
    /* 配置输入捕获通道1 */
    /*
     * 通道1映射到TI1输入
     * 输入捕获预分频不分频
     * 检测上升沿触发
     * 启用输入滤波
     */
    TIM2_ICInit(
        TIM2_CHANNEL_1,                  /* 使用通道1 */
        TIM2_ICPOLARITY_RISING,          /* 上升沿捕获 */
        TIM2_ICSELECTION_DIRECTTI,       /* 直接映射到TI1 */
        TIM2_ICPSC_DIV1,                 /* 不分频 */
        0x00                             /* 添加滤波，提高低频信号稳定性 */
    );
    
    /* 清除捕获中断标志 */
    TIM2_ClearFlag(TIM2_FLAG_CC1);
    
    /* 使能捕获中断 */
    TIM2_ITConfig(TIM2_IT_CC1, ENABLE);
    
    /* 使能更新中断(用于超时检测) */
    TIM2_ITConfig(TIM2_IT_UPDATE, ENABLE);
    
    /* 清除所有中断标志 */
    TIM2_ClearITPendingBit(TIM2_IT_CC1);
    TIM2_ClearITPendingBit(TIM2_IT_UPDATE);
    
    /* 使能计数器 */
    TIM2_Cmd(ENABLE);
}


