<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>2</fileVersion>
    <fileChecksum>522211113</fileChecksum>
    <configuration>
        <name>Debug</name>
        <outputs>
            <file>$PROJ_DIR$\..\USER\stm8s_it.c</file>
            <file>$PROJ_DIR$\..\USER\uart.c</file>
            <file>$PROJ_DIR$\..\LIB\src\stm8s_tim2.c</file>
            <file>$PROJ_DIR$\..\BSP\tim.c</file>
            <file>$PROJ_DIR$\..\BSP\bka30.c</file>
            <file>$PROJ_DIR$\..\LIB\src\stm8s_adc1.c</file>
            <file>$PROJ_DIR$\..\LIB\src\stm8s_clk.c</file>
            <file>$PROJ_DIR$\..\LIB\src\stm8s_exti.c</file>
            <file>$PROJ_DIR$\..\LIB\src\stm8s_gpio.c</file>
            <file>$PROJ_DIR$\..\LIB\src\stm8s_tim1.c</file>
            <file>$PROJ_DIR$\..\LIB\src\stm8s_uart1.c</file>
            <file>$PROJ_DIR$\..\USER\delay.c</file>
            <file>$PROJ_DIR$\..\USER\main.c</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_uart1.pbi</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_uart1.o</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_flash.pbi</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_tim1.o</file>
            <file>$TOOLKIT_DIR$\inc\c\ycheck.h</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_adc1.pbi</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_adc1.o</file>
            <file>$PROJ_DIR$\..\LIB\inc\stm8s_conf.h</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_exti.o</file>
            <file>$PROJ_DIR$\Debug\Exe\tachometer.out</file>
            <file>$TOOLKIT_DIR$\lib\dbgstm8smd.a</file>
            <file>$TOOLKIT_DIR$\inc\c\xencoding_limits.h</file>
            <file>$PROJ_DIR$\Debug\Obj\delay.pbi</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_itc.pbi</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_clk.o</file>
            <file>$PROJ_DIR$\..\LIB\inc\stm8s_adc1.h</file>
            <file>$TOOLKIT_DIR$\config\lnkstm8s003f3.icf</file>
            <file>$PROJ_DIR$\Debug\Obj\uart.pbi</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_tim2.pbi</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_itc.o</file>
            <file>$TOOLKIT_DIR$\inc\c\DLib_Defaults.h</file>
            <file>$TOOLKIT_DIR$\inc\c\ystdio.h</file>
            <file>$PROJ_DIR$\Debug\Obj\uart1.pbi</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_it.o</file>
            <file>$PROJ_DIR$\..\USER\main.h</file>
            <file>$PROJ_DIR$\..\LIB\inc\stm8s_itc.h</file>
            <file>$PROJ_DIR$\..\BSP\tim.h</file>
            <file>$TOOLKIT_DIR$\inc\c\DLib_Threads.h</file>
            <file>$PROJ_DIR$\..\LIB\src\stm8s_flash.c</file>
            <file>$TOOLKIT_DIR$\inc\c\stdio.h</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_exti.__cstat.et</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_tim1.pbi</file>
            <file>$TOOLKIT_DIR$\inc\c\math.h</file>
            <file>$PROJ_DIR$\Debug\Obj\main.__cstat.et</file>
            <file>$PROJ_DIR$\..\BSP\bka30.h</file>
            <file>$TOOLKIT_DIR$\inc\c\DLib_Product.h</file>
            <file>$PROJ_DIR$\..\LIB\inc\stm8s_exti.h</file>
            <file>$PROJ_DIR$\Debug\Obj\uart.o</file>
            <file>$PROJ_DIR$\..\USER\stm8s_it.h</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_gpio.pbi</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_tim1.__cstat.et</file>
            <file>$PROJ_DIR$\Debug\Obj\delay.__cstat.et</file>
            <file>$PROJ_DIR$\..\LIB\src\stm8s_itc.c</file>
            <file>$PROJ_DIR$\Debug\Obj\bka30.pbi</file>
            <file>$TOOLKIT_DIR$\lib\dlstm8smn.h</file>
            <file>$PROJ_DIR$\..\LIB\inc\stm8s_uart1.h</file>
            <file>$PROJ_DIR$\Debug\Obj\main.pbi</file>
            <file>$PROJ_DIR$\Debug\Obj\tim.pbi</file>
            <file>$PROJ_DIR$\Debug\Obj\bka30.__cstat.et</file>
            <file>$PROJ_DIR$\Debug\Obj\tim.__cstat.et</file>
            <file>$PROJ_DIR$\..\USER\delay.h</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_it.pbi</file>
            <file>$PROJ_DIR$\..\BSP\uart1.c</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_clk.__cstat.et</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_gpio.__cstat.et</file>
            <file>$PROJ_DIR$\Debug\Obj\tachometer.pbd</file>
            <file>$TOOLKIT_DIR$\inc\c\yvals.h</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_uart1.__cstat.et</file>
            <file>$PROJ_DIR$\..\LIB\inc\stm8s_flash.h</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_clk.pbi</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_exti.pbi</file>
            <file>$TOOLKIT_DIR$\lib\dlstm8smn.a</file>
            <file>$PROJ_DIR$\..\LIB\inc\stm8s_tim2.h</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_tim2.o</file>
            <file>$TOOLKIT_DIR$\inc\c\ymath.h</file>
            <file>$PROJ_DIR$\Debug\List\tachometer.map</file>
            <file>$PROJ_DIR$\Debug\Obj\bka30.o</file>
            <file>$PROJ_DIR$\..\LIB\inc\stm8s_gpio.h</file>
            <file>$PROJ_DIR$\Debug\Obj\delay.o</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_gpio.o</file>
            <file>$PROJ_DIR$\Debug\Obj\tim.o</file>
            <file>$TOOLKIT_DIR$\inc\c\string.h</file>
            <file>$PROJ_DIR$\..\USER\uart.h</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_flash.o</file>
            <file>$PROJ_DIR$\Debug\Obj\main.o</file>
            <file>$PROJ_DIR$\Debug\Obj\uart.__cstat.et</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_it.__cstat.et</file>
            <file>$PROJ_DIR$\..\LIB\inc\stm8s_clk.h</file>
            <file>$PROJ_DIR$\..\LIB\inc\stm8s_tim1.h</file>
            <file>$TOOLKIT_DIR$\inc\c\intrinsics.h</file>
            <file>$PROJ_DIR$\Debug\Obj\uart1.o</file>
            <file>$TOOLKIT_DIR$\inc\c\ysizet.h</file>
            <file>$PROJ_DIR$\Debug\Obj\stm8s_tim2.__cstat.et</file>
            <file>$PROJ_DIR$\..\LIB\inc\stm8s.h</file>
            <file>$TOOLKIT_DIR$\inc\c\xtgmath.h</file>
            <file>$PROJ_DIR$\..\BSP\uart1.h</file>
        </outputs>
        <file>
            <name>[ROOT_NODE]</name>
            <outputs>
                <tool>
                    <name>ILINK</name>
                    <file> 22 78</file>
                </tool>
            </outputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\USER\stm8s_it.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 36</file>
                </tool>
                <tool>
                    <name>__cstat</name>
                    <file> 89</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 64</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 51 96 20 28 90 49 71 80 38 91 75 58 92</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 96 20 38 90 71 75 92 28 49 80 91 58 51</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\USER\uart.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 50</file>
                </tool>
                <tool>
                    <name>__cstat</name>
                    <file> 88</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 30</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 85 37 96 20 28 90 49 71 80 38 91 75 58 92 63 47 39 42 17 69 33 57 48 24 40 94 34 84</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 75 85 96 84 71 20 39 90 38 34 17 24 48 42 37 63 47 92 28 49 80 91 58 69 57 94 33 40</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_tim2.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 76</file>
                </tool>
                <tool>
                    <name>__cstat</name>
                    <file> 95</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 31</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 75 96 20 28 90 49 71 80 38 91 58 92</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 58 96 28 80 92 49 91 75 20 90 71 38</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\BSP\tim.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 83</file>
                </tool>
                <tool>
                    <name>__cstat</name>
                    <file> 62</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 60</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 39 37 96 20 28 90 49 71 80 38 91 75 58 92 63 85 47 42 17 69 33 57 48 24 40 94 34 84</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 91 37 63 92 96 42 49 47 28 80 58 94 17 24 40 20 85 39 84 90 71 38 75 69 34 33 57 48</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\BSP\bka30.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 79</file>
                </tool>
                <tool>
                    <name>__cstat</name>
                    <file> 61</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 56</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 47 37 96 20 28 90 49 71 80 38 91 75 58 92 63 85 39 42 17 69 33 57 48 24 40 94 34 84</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 20 37 39 71 96 85 84 75 34 17 24 48 63 47 42 90 38 69 57 92 28 49 80 91 58 94 33 40</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_adc1.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 19</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 18</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 28 96 20 90 49 71 80 38 91 75 58 92</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 58 28 80 92 49 91 96 20 90 71 38 75</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_clk.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 27</file>
                </tool>
                <tool>
                    <name>__cstat</name>
                    <file> 66</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 72</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 90 96 20 28 49 71 80 38 91 75 58 92</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 75 96 71 20 90 38 92 28 49 80 91 58</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_exti.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 21</file>
                </tool>
                <tool>
                    <name>__cstat</name>
                    <file> 43</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 73</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 49 96 20 28 90 71 80 38 91 75 58 92</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 49 96 92 91 28 80 58 20 90 71 38 75</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_gpio.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 82</file>
                </tool>
                <tool>
                    <name>__cstat</name>
                    <file> 67</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 52</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 80 96 20 28 90 49 71 38 91 75 58 92</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 28 58 96 80 92 49 91 20 90 71 38 75</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_tim1.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 16</file>
                </tool>
                <tool>
                    <name>__cstat</name>
                    <file> 53</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 44</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 91 96 20 28 90 49 71 80 38 75 58 92</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 80 96 28 58 92 49 91 20 90 71 38 75</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_uart1.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 14</file>
                </tool>
                <tool>
                    <name>__cstat</name>
                    <file> 70</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 13</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 58 96 20 28 90 49 71 80 38 91 75 92</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 96 91 92 49 28 80 58 20 90 71 38 75</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\USER\delay.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 81</file>
                </tool>
                <tool>
                    <name>__cstat</name>
                    <file> 54</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 25</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 63 37 96 20 28 90 49 71 80 38 91 75 58 92 85 47 39 42 17 69 33 57 48 24 40 94 34 84</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 96 38 37 20 39 90 85 84 71 75 94 17 24 40 63 47 42 92 28 49 80 91 58 69 34 33 57 48</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\USER\main.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 87</file>
                </tool>
                <tool>
                    <name>__cstat</name>
                    <file> 46</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 59</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 37 96 20 28 90 49 71 80 38 91 75 58 92 63 85 47 39 42 17 69 33 57 48 24 40 94 34 84 45 77 97</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 42 96 17 63 49 69 47 92 91 33 45 20 85 39 84 28 80 58 37 94 40 97 90 71 38 75 34 24 57 48 77</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\Debug\Exe\tachometer.out</name>
            <outputs>
                <tool>
                    <name>ILINK</name>
                    <file> 78</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ILINK</name>
                    <file> 29 79 81 87 19 27 21 82 36 16 76 14 83 50 74 23</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_flash.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 86</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 15</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 71 96 20 28 90 49 80 38 91 75 58 92</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 38 96 20 90 71 75 92 28 49 80 91 58</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_itc.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 32</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 26</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>BICOMP</name>
                    <file> 91 90 96 92 80 58 38 20 49 75</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\..\BSP\uart1.c</name>
            <outputs>
                <tool>
                    <name>ICCSTM8</name>
                    <file> 93</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 35</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>BICOMP</name>
                    <file> 94 63 80 40 92 58 20 90 91 96 42 37 98 39 49 38 75 17 33 69 34 24 57 48</file>
                </tool>
            </inputs>
        </file>
    </configuration>
    <configuration>
        <name>Release</name>
        <outputs />
        <forcedrebuild>
            <name>[MULTI_TOOL]</name>
            <tool>ILINK</tool>
        </forcedrebuild>
    </configuration>
</project>
