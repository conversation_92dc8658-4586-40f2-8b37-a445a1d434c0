<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>3</fileVersion>
    <configuration>
        <name>Debug</name>
        <toolchain>
            <name>STM8</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>General</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>2</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>GenDeviceSelectMenu</name>
                    <state>STM8S003F3	STM8S003F3</state>
                </option>
                <option>
                    <name>GenCodeModel</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>GenDataModel</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ExePath</name>
                    <state>Debug\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>Debug\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>Debug\List</state>
                </option>
                <option>
                    <name>GenRuntimeLibSelect</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GenRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GenRTDescription</name>
                    <state>Use the normal configuration of the C/EC++ runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
                </option>
                <option>
                    <name>GenRTConfigPath</name>
                    <state>$TOOLKIT_DIR$\LIB\dlstm8smn.h</state>
                </option>
                <option>
                    <name>GenLibInFormatter</name>
                    <version>0</version>
                    <state>5</state>
                </option>
                <option>
                    <name>GenLibInFormatterDescription</name>
                    <state>No specifier n, no float, no scan set, no assignment suppressing.</state>
                </option>
                <option>
                    <name>GenLibOutFormatter</name>
                    <version>0</version>
                    <state>5</state>
                </option>
                <option>
                    <name>GenLibOutFormatterDescription</name>
                    <state>No specifier a or A, no specifier n, no float.</state>
                </option>
                <option>
                    <name>GenStackSize</name>
                    <state>0x100</state>
                </option>
                <option>
                    <name>GenHeapSize</name>
                    <state>0x100</state>
                </option>
                <option>
                    <name>GeneralEnableMisra</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraVerbose</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraVer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>GeneralMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>GenMathFunctionVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>GenMathFunctionDescription</name>
                    <state>Default variants of cos, sin, tan, log, log10, pow, and exp.</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCSTM8</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>9</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IccRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccLanguageConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCharIs</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccOptLevel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccOptStrategy</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>IccOptLevelSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccOptAllowList</name>
                    <version>0</version>
                    <state>000000</state>
                </option>
                <option>
                    <name>IccGenerateDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccOutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>IccCodeModel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccDataModel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    <state>$PROJ_DIR$\..\lib\inc</state>
                    <state>$PROJ_DIR$\..\lib\src</state>
                    <state>$PROJ_DIR$\..\lib</state>
                    <state>$PROJ_DIR$\..\user</state>
                    <state>$PROJ_DIR$\..\bsp</state>
                    <state>$PROJ_DIR$\..\iar</state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CompilerMisraOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CompilerMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>CompilerMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>IccUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCppDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccNoVregs</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>IccOptNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ASTM8</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>2</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>AsmCaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmAllowMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmAllowDirectives</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmMacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmListFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListNoDiagnostics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListIncludeCrossRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListMacroDefinitions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListNoMacroExpansion</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListAssembledOnly</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListTruncateMultiline</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmStdIncludeIgnore</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmIncludePath</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmPreprocOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmPreprocComment</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmDiagnosticsSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsError</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmLimitNumberOfErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmMaxNumberOfErrors</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AsmCodeModel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmDataModel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmOutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>AsmUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmPreInclude</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>2</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state>tachometer.srec</state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
            </data>
        </settings>
        <settings>
            <name>BICOMP</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <prebuild></prebuild>
                <postbuild></postbuild>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>3</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XLinkMisraHandler</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>tachometer.out</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>$TOOLKIT_DIR$\config\lnkstm8s003f3.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>__iar_program_start</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCspyDebugSupportEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkCspyBufferedWrite</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Unitialized###</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BILINK</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
    </configuration>
    <configuration>
        <name>Release</name>
        <toolchain>
            <name>STM8</name>
        </toolchain>
        <debug>0</debug>
        <settings>
            <name>General</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>2</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>GenDeviceSelectMenu</name>
                    <state></state>
                </option>
                <option>
                    <name>GenCodeModel</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>GenDataModel</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ExePath</name>
                    <state>Release\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>Release\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>Release\List</state>
                </option>
                <option>
                    <name>GenRuntimeLibSelect</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GenRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GenRTDescription</name>
                    <state></state>
                </option>
                <option>
                    <name>GenRTConfigPath</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLibInFormatter</name>
                    <version>0</version>
                    <state>5</state>
                </option>
                <option>
                    <name>GenLibInFormatterDescription</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLibOutFormatter</name>
                    <version>0</version>
                    <state>5</state>
                </option>
                <option>
                    <name>GenLibOutFormatterDescription</name>
                    <state></state>
                </option>
                <option>
                    <name>GenStackSize</name>
                    <state>###Uninitialized###</state>
                </option>
                <option>
                    <name>GenHeapSize</name>
                    <state>###Uninitialized###</state>
                </option>
                <option>
                    <name>GeneralEnableMisra</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraVerbose</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraVer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>GeneralMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>GenMathFunctionVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>GenMathFunctionDescription</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCSTM8</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>9</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IccRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccLanguageConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCharIs</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccOptLevel</name>
                    <state>3</state>
                </option>
                <option>
                    <name>IccOptStrategy</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IccOptLevelSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccOptAllowList</name>
                    <version>0</version>
                    <state>111111</state>
                </option>
                <option>
                    <name>IccGenerateDebugInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccOutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IccCodeModel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccDataModel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCDefines</name>
                    <state>NDEBUG</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    <state></state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CompilerMisraOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CompilerMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>CompilerMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>IccUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCppDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccNoVregs</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>IccOptNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ASTM8</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>2</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>AsmCaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmAllowMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmAllowDirectives</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmMacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmDebugInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListNoDiagnostics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListIncludeCrossRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListMacroDefinitions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListNoMacroExpansion</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListAssembledOnly</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListTruncateMultiline</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmStdIncludeIgnore</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmIncludePath</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmPreprocOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmPreprocComment</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmDiagnosticsSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsError</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmLimitNumberOfErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmMaxNumberOfErrors</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AsmCodeModel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmDataModel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmOutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>AsmUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmPreInclude</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>2</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
            </data>
        </settings>
        <settings>
            <name>BICOMP</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <prebuild></prebuild>
                <postbuild></postbuild>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>3</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XLinkMisraHandler</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>###Unitialized###</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>lnk0t.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state></state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCspyDebugSupportEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCspyBufferedWrite</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Unitialized###</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BILINK</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
    </configuration>
    <group>
        <name>BSP</name>
        <file>
            <name>$PROJ_DIR$\..\BSP\bka30.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\BSP\tim.c</name>
        </file>
    </group>
    <group>
        <name>LIB</name>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_adc1.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_clk.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_exti.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_gpio.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_tim1.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_tim2.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\LIB\src\stm8s_uart1.c</name>
        </file>
    </group>
    <group>
        <name>USER</name>
        <file>
            <name>$PROJ_DIR$\..\USER\delay.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\USER\main.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\USER\stm8s_it.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\USER\uart.c</name>
        </file>
    </group>
</project>
