<?xml version="1.0"?>
<settings>
    <InterruptLog>
        <LogEnabled>0</LogEnabled>
        <GraphEnabled>0</GraphEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
        <SumSortOrder>0</SumSortOrder>
    </InterruptLog>
    <DataLog>
        <LogEnabled>0</LogEnabled>
        <GraphEnabled>0</GraphEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
    </DataLog>
    <Stack>
        <FillEnabled>0</FillEnabled>
        <OverflowWarningsEnabled>1</OverflowWarningsEnabled>
        <WarningThreshold>90</WarningThreshold>
        <SpWarningsEnabled>1</SpWarningsEnabled>
        <WarnLogOnly>1</WarnLogOnly>
        <UseTrigger>1</UseTrigger>
        <TriggerName>main</TriggerName>
        <LimitSize>0</LimitSize>
        <ByteLimit>50</ByteLimit>
    </Stack>
    <Breakpoints2>
        <Count>0</Count>
    </Breakpoints2>
    <Interrupts>
        <Enabled>1</Enabled>
    </Interrupts>
    <MemConfig>
        <Base>1</Base>
        <Manual>0</Manual>
        <Ddf>1</Ddf>
        <TypeViol>0</TypeViol>
        <Stop>1</Stop>
    </MemConfig>
    <Trace1>
        <Enabled>0</Enabled>
        <ShowSource>1</ShowSource>
    </Trace1>
    <Simulator>
        <Freq>16000000</Freq>
        <FreqHi>0</FreqHi>
        <MultiCoreRunAll>1</MultiCoreRunAll>
    </Simulator>
    <DebugChecksum>
        <Checksum>3835413277</Checksum>
    </DebugChecksum>
    <CallStack>
        <ShowArgs>0</ShowArgs>
    </CallStack>
    <Disassembly>
        <MixedMode>1</MixedMode>
    </Disassembly>
    <DataSample>
        <LogEnabled>0</LogEnabled>
        <GraphEnabled>0</GraphEnabled>
        <ShowTimeLog>1</ShowTimeLog>
    </DataSample>
    <Breakpoints>
        <Count>0</Count>
    </Breakpoints>
    <LogFile>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
        <Category>_ 0</Category>
    </LogFile>
    <TermIOLog>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
    </TermIOLog>
    <Aliases>
        <A0>_ "C:\Users\<USER>\Desktop\5.6\LIB\src\stm8s_tim1.c" ""</A0>
        <A1>_ "C:\Users\<USER>\Desktop\5.6\LIB\src\stm8s_uart1.c" ""</A1>
        <A2>_ "C:\Users\<USER>\Desktop\5.16\LIB\src\stm8s_adc1.c" ""</A2>
        <A3>_ "C:\Users\<USER>\Desktop\5.16\LIB\src\stm8s_clk.c" ""</A3>
        <A4>_ "C:\Users\<USER>\Desktop\5.16\LIB\src\stm8s_gpio.c" ""</A4>
        <A5>_ "C:\Users\<USER>\Desktop\5.16\USER\main.c" ""</A5>
        <Count>6</Count>
        <SuppressDialog>1</SuppressDialog>
    </Aliases>
    <DebuggerSettings>
        <DisableInterruptsWhenStepping>0</DisableInterruptsWhenStepping>
        <LeaveTargetRunning>0</LeaveTargetRunning>
    </DebuggerSettings>
</settings>
