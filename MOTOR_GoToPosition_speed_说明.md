# MOTOR_GoToPosition_speed 函数说明

## 功能概述
`MOTOR_GoToPosition_speed` 是新增的电机控制函数，用于让步进电机以最大速度运行到指定位置，跳过加减速过程。

## 函数原型
```c
void MOTOR_GoToPosition_speed(u16 pos);
```

## 参数说明
- `pos`: 目标位置（步数），范围：0 ~ 3240

## 功能特点

### 🚀 **最大速度运行**
- 直接使用 `MAX_SPEED` 常量作为运行速度
- 跳过所有加速和减速阶段
- 适用于需要快速响应的场景

### ⚡ **与普通函数的区别**

| 特性 | MOTOR_GoToPosition | MOTOR_GoToPosition_speed |
|------|-------------------|-------------------------|
| 速度控制 | 加减速曲线 | 固定最大速度 |
| 运行时间 | 较长（平滑） | 较短（快速） |
| 适用场景 | 正常显示 | 快速定位/自检 |
| 滤波功能 | 有防抖动滤波 | 无滤波 |

## 使用示例

### 基本使用
```c
// 快速移动到位置1000
MOTOR_GoToPosition_speed(1000);

// 等待运动完成
while(MOTOR_IsRunning()) {
    MOTOR_Update();
}
```

### 自检程序中的应用
```c
void step_init(void)
{
    // 归零
    MOTOR_Homing();
    while(MOTOR_IsRunning() || MOTOR_IsHoming()) {
        MOTOR_Update();
    }
    
    // 快速移动到最大位置
    MOTOR_GoToPosition_speed(MAX_STEP);
    while(MOTOR_IsRunning()) {
        MOTOR_Update();
    }
    
    // 快速返回零点
    MOTOR_GoToPosition_speed(0);
    while(MOTOR_IsRunning()) {
        MOTOR_Update();
    }
}
```

## 技术实现

### 新增变量
- `is_max_speed_mode`: 标识是否处于最大速度模式

### 新增函数
- `MOTOR_Advance_MaxSpeed()`: 最大速度模式的步进控制函数

### 修改的函数
- `MOTOR_Update()`: 根据模式选择不同的前进函数

## 注意事项

1. **无防抖动滤波**: 该函数不包含方向变化的滤波机制，会立即响应位置变化
2. **适用场景**: 主要用于自检、快速定位等不需要平滑运动的场合
3. **兼容性**: 与现有函数完全兼容，不影响原有功能
4. **速度设置**: 速度由 `MAX_SPEED` 全局变量控制

## 应用建议

- **自检程序**: 使用 `MOTOR_GoToPosition_speed` 快速完成自检
- **正常显示**: 使用 `MOTOR_GoToPosition` 保证平滑显示
- **紧急归零**: 掉电时可使用 `MOTOR_GoToPosition_speed(0)` 快速归零
