/*
 * 断电归零功能测试示例
 * 
 * 此文件展示如何使用新的断电归零功能
 */

#include "main.h"

// 测试用的全局变量
volatile u16 test_vol = 150;  // 模拟电压值
volatile _Bool test_power_lost = 0;

/*
 * 模拟断电测试函数
 * 演示断电归零的完整流程
 */
void test_power_loss_recovery(void)
{
    // 1. 正常运行状态 - 电机移动到中间位置
    printf("1. 正常运行 - 移动到位置1620\n");
    MOTOR_GoToPosition(1620);
    
    // 等待到达目标位置
    while(MOTOR_IsRunning()) {
        MOTOR_Update();
    }
    printf("   到达目标位置: %d\n", 1620);
    
    // 2. 模拟断电情况
    printf("2. 模拟断电 - 电压降到100\n");
    test_vol = 100;  // 模拟电压下降
    
    // 断电检测逻辑（与main.c中相同）
    if(test_vol < 120) {
        if(!test_power_lost) {
            test_power_lost = 1;
            printf("   检测到断电！启动紧急归零...\n");
            MOTOR_EmergencyHome();  // 启动紧急归零
        }
    }
    
    // 等待紧急归零完成
    while(MOTOR_IsRunning()) {
        MOTOR_Update();
    }
    printf("   紧急归零完成！当前位置: 0\n");
    
    // 3. 模拟电源恢复
    printf("3. 模拟电源恢复 - 电压恢复到150\n");
    test_vol = 150;  // 模拟电压恢复
    
    if(test_vol >= 120) {
        if(test_power_lost) {
            test_power_lost = 0;
            printf("   电源已恢复！系统可以正常工作\n");
        }
    }
    
    // 4. 恢复正常运行
    printf("4. 恢复正常运行 - 移动到位置800\n");
    MOTOR_GoToPosition(800);
    
    while(MOTOR_IsRunning()) {
        MOTOR_Update();
    }
    printf("   测试完成！当前位置: 800\n");
}

/*
 * 速度对比测试
 * 比较不同归零方式的速度差异
 */
void test_speed_comparison(void)
{
    u32 start_time, end_time;
    
    printf("\n=== 归零速度对比测试 ===\n");
    
    // 先移动到最大位置
    MOTOR_GoToPosition_speed(3240);
    while(MOTOR_IsRunning()) {
        MOTOR_Update();
    }
    
    // 1. 测试普通归零
    printf("1. 普通归零 MOTOR_GoToPosition(0):\n");
    start_time = MOTOR_GetStepCount();
    MOTOR_GoToPosition(0);
    while(MOTOR_IsRunning()) {
        MOTOR_Update();
    }
    end_time = MOTOR_GetStepCount();
    printf("   用时: %lu 个计数周期\n", end_time - start_time);
    
    // 回到最大位置
    MOTOR_GoToPosition_speed(3240);
    while(MOTOR_IsRunning()) {
        MOTOR_Update();
    }
    
    // 2. 测试快速归零
    printf("2. 快速归零 MOTOR_GoToPosition_speed(0):\n");
    start_time = MOTOR_GetStepCount();
    MOTOR_GoToPosition_speed(0);
    while(MOTOR_IsRunning()) {
        MOTOR_Update();
    }
    end_time = MOTOR_GetStepCount();
    printf("   用时: %lu 个计数周期\n", end_time - start_time);
    
    // 回到最大位置
    MOTOR_GoToPosition_speed(3240);
    while(MOTOR_IsRunning()) {
        MOTOR_Update();
    }
    
    // 3. 测试紧急归零
    printf("3. 紧急归零 MOTOR_EmergencyHome():\n");
    start_time = MOTOR_GetStepCount();
    MOTOR_EmergencyHome();
    while(MOTOR_IsRunning()) {
        MOTOR_Update();
    }
    end_time = MOTOR_GetStepCount();
    printf("   用时: %lu 个计数周期\n", end_time - start_time);
    
    printf("=== 测试完成 ===\n");
}

/*
 * 主测试函数
 */
void run_power_loss_tests(void)
{
    printf("开始断电归零功能测试...\n\n");
    
    // 初始化电机
    MOTOR_Init();
    
    // 运行断电恢复测试
    test_power_loss_recovery();
    
    // 运行速度对比测试
    test_speed_comparison();
    
    printf("\n所有测试完成！\n");
}

/*
 * 实际应用中的断电检测代码示例
 * （从main.c中提取的关键部分）
 */
void power_monitor_example(void)
{
    static volatile _Bool is_power_lost = 0;
    u16 vol = Read_ADC(ADC1_CHANNEL_6);  // 读取电压
    
    if(vol < 120)   // 断电检测阈值
    {
        if(!is_power_lost)  // 首次检测到断电
        {
            is_power_lost = 1;          // 设置断电标志
            MOTOR_EmergencyHome();      // 启动紧急归零
            
            // 可以在这里添加其他断电处理逻辑
            // 例如：保存重要数据、关闭外设等
        }
        // 断电期间不处理其他传感器数据
    }
    else  // 电源正常
    {
        if(is_power_lost)  // 电源恢复
        {
            is_power_lost = 0;  // 清除断电标志
            
            // 可以在这里添加电源恢复后的初始化逻辑
            // 例如：重新校准传感器、恢复正常工作模式等
        }
        
        // 正常处理传感器数据和电机控制
        // ...
    }
}
